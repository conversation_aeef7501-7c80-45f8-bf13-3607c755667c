#!/usr/bin/env python3
"""
Hybrid Tracking and Classification Pipeline
==========================================

This pipeline combines:
1. Hand-to-Product ONNX model for stable object tracking
2. Trained Amino YOLO model for accurate product classification within tracked bounding boxes
3. Multi-camera quadrant display with performance metrics

Key Features:
- ONNX model provides stable tracking without box disappearing issues
- YOLO model provides accurate product classification only inside tracked regions
- Prevents ghost trails and maintains consistent bounding box tracking
- Real-time performance metrics and confidence scoring
"""

import sys
import os
import time
import cv2
import numpy as np
import torch
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import logging
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor

# Add Hand_Test to path for YOLOv5
HAND_TEST_DIR = Path("Hand_Test")
sys.path.insert(0, str(HAND_TEST_DIR))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hybrid_pipeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

try:
    import onnxruntime as ort
    logger.info("✅ ONNX Runtime imported successfully")
except ImportError:
    logger.error("❌ ONNX Runtime not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "onnxruntime"])
    import onnxruntime as ort
    logger.info("✅ ONNX Runtime installed and imported")


class HybridTracker:
    """Hybrid tracker combining ONNX tracking with YOLO classification"""
    
    def __init__(self, onnx_model_path: str, yolo_model_path: str):
        self.onnx_model_path = onnx_model_path
        self.yolo_model_path = Path(yolo_model_path)
        
        # Initialize models
        self.onnx_session = None
        self.yolo_model = None
        self.load_models()
        
        # Tracking parameters
        self.confidence_threshold = 0.25
        self.iou_threshold = 0.45
        self.track_timeout = 30
        self.active_tracks = {}
        self.next_track_id = 1
        self.frame_count = 0
        
        # Motion detection for tracking stability
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
            detectShadows=True, varThreshold=50
        )
        
    def load_models(self) -> bool:
        """Load both ONNX and YOLO models"""
        try:
            # Load ONNX model for tracking
            if not os.path.exists(self.onnx_model_path):
                logger.error(f"ONNX model not found: {self.onnx_model_path}")
                return False
                
            self.onnx_session = ort.InferenceSession(self.onnx_model_path)
            self.onnx_input_name = self.onnx_session.get_inputs()[0].name
            self.onnx_output_names = [output.name for output in self.onnx_session.get_outputs()]
            
            logger.info(f"✅ ONNX model loaded: {self.onnx_model_path}")
            
            # Load YOLO model for classification
            if not self.yolo_model_path.exists():
                logger.error(f"YOLO model not found: {self.yolo_model_path}")
                return False
                
            self.yolo_model = torch.hub.load(
                str(HAND_TEST_DIR), 
                'custom', 
                path=str(self.yolo_model_path), 
                source='local'
            )
            self.yolo_model.conf = 0.25
            self.yolo_model.iou = 0.45
            
            logger.info(f"✅ YOLO model loaded: {self.yolo_model_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False
    
    def preprocess_frame_onnx(self, frame: np.ndarray) -> np.ndarray:
        """Preprocess frame for ONNX model"""
        # Resize to model input size (typically 640x640)
        input_size = 640
        resized = cv2.resize(frame, (input_size, input_size))
        
        # Convert BGR to RGB and normalize
        rgb_frame = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        normalized = rgb_frame.astype(np.float32) / 255.0
        
        # Add batch dimension and transpose to NCHW
        input_data = np.transpose(normalized, (2, 0, 1))
        input_data = np.expand_dims(input_data, axis=0)
        
        return input_data
    
    def postprocess_onnx_detections(self, outputs: List[np.ndarray], 
                                   original_shape: Tuple[int, int]) -> List[Dict]:
        """Postprocess ONNX model outputs to get detections"""
        detections = []
        
        try:
            # ONNX output format: [batch, num_detections, 85] where 85 = 4 bbox + 1 conf + 80 classes
            predictions = outputs[0][0]  # Remove batch dimension
            
            orig_h, orig_w = original_shape
            input_size = 640
            
            for detection in predictions:
                # Extract bbox and confidence
                x_center, y_center, width, height = detection[:4]
                confidence = detection[4]
                
                if confidence < self.confidence_threshold:
                    continue
                
                # Convert from normalized coordinates to original image coordinates
                x_center = x_center * orig_w / input_size
                y_center = y_center * orig_h / input_size
                width = width * orig_w / input_size
                height = height * orig_h / input_size
                
                # Convert to x1, y1, x2, y2 format
                x1 = int(x_center - width / 2)
                y1 = int(y_center - height / 2)
                x2 = int(x_center + width / 2)
                y2 = int(y_center + height / 2)
                
                # Ensure coordinates are within image bounds
                x1 = max(0, min(x1, orig_w - 1))
                y1 = max(0, min(y1, orig_h - 1))
                x2 = max(0, min(x2, orig_w - 1))
                y2 = max(0, min(y2, orig_h - 1))
                
                # Get class with highest probability
                class_scores = detection[5:]
                class_id = np.argmax(class_scores)
                class_confidence = class_scores[class_id]
                
                # Filter by class confidence
                if class_confidence < 0.3:
                    continue
                
                detections.append({
                    'bbox': [x1, y1, x2, y2],
                    'confidence': float(confidence),
                    'class_id': int(class_id),
                    'class_confidence': float(class_confidence),
                    'class_name': 'product'  # Generic for ONNX tracking
                })
                
        except Exception as e:
            logger.debug(f"Error postprocessing ONNX detections: {e}")
            
        return detections
    
    def detect_with_onnx(self, frame: np.ndarray) -> List[Dict]:
        """Run ONNX detection for tracking"""
        try:
            # Preprocess frame
            input_data = self.preprocess_frame_onnx(frame)
            
            # Run inference
            outputs = self.onnx_session.run(self.onnx_output_names, {self.onnx_input_name: input_data})
            
            # Postprocess results
            detections = self.postprocess_onnx_detections(outputs, frame.shape[:2])
            
            return detections
            
        except Exception as e:
            logger.error(f"Error in ONNX detection: {e}")
            return []
    
    def classify_with_yolo(self, frame: np.ndarray, bbox: List[int]) -> Dict:
        """Run YOLO classification within tracked bounding box"""
        try:
            x1, y1, x2, y2 = bbox
            
            # Extract region of interest
            roi = frame[y1:y2, x1:x2]
            
            if roi.size == 0:
                return {'class_name': 'unknown', 'confidence': 0.0}
            
            # Run YOLO inference on ROI
            results = self.yolo_model(roi)
            
            # Extract best detection from results
            if hasattr(results, 'xyxy') and len(results.xyxy[0]) > 0:
                detections = results.xyxy[0].cpu().numpy()
                
                # Get detection with highest confidence
                best_detection = detections[np.argmax(detections[:, 4])]
                confidence = float(best_detection[4])
                
                return {
                    'class_name': 'Amino_Energy_Grape',
                    'confidence': confidence
                }
            else:
                return {'class_name': 'unknown', 'confidence': 0.0}
                
        except Exception as e:
            logger.debug(f"Error in YOLO classification: {e}")
            return {'class_name': 'unknown', 'confidence': 0.0}
    
    def update_tracks(self, detections: List[Dict], frame: np.ndarray) -> List[Dict]:
        """Update object tracks with new detections and YOLO classification"""
        self.frame_count += 1
        updated_tracks = []
        
        if len(detections) == 0:
            # No detections - decay existing tracks
            for track_id, track in self.active_tracks.items():
                track['frames_since_update'] += 1
                
                if track['frames_since_update'] < self.track_timeout:
                    # Decay confidence
                    decay_factor = 0.95 ** track['frames_since_update']
                    track['confidence'] = max(track['confidence'] * decay_factor, 0.1)
                    updated_tracks.append(track)
                    
            self.active_tracks = {track['track_id']: track for track in updated_tracks}
            return updated_tracks
        
        # Match detections to existing tracks
        for detection in detections:
            best_track = None
            best_iou = 0.0
            
            # Find best matching track
            for track_id, track in self.active_tracks.items():
                iou = self.calculate_iou(detection['bbox'], track['bbox'])
                if iou > best_iou and iou > 0.3:
                    best_iou = iou
                    best_track = track
            
            if best_track:
                # Update existing track
                best_track['bbox'] = detection['bbox']
                best_track['confidence'] = detection['confidence']
                best_track['frames_since_update'] = 0
                best_track['age'] += 1
                
                # Run YOLO classification within tracked bbox
                classification = self.classify_with_yolo(frame, detection['bbox'])
                best_track['yolo_classification'] = classification
                
                updated_tracks.append(best_track)
            else:
                # Create new track
                new_track = {
                    'track_id': self.next_track_id,
                    'bbox': detection['bbox'],
                    'confidence': detection['confidence'],
                    'frames_since_update': 0,
                    'age': 1,
                    'yolo_classification': self.classify_with_yolo(frame, detection['bbox'])
                }
                self.next_track_id += 1
                updated_tracks.append(new_track)
        
        # Update active tracks
        self.active_tracks = {track['track_id']: track for track in updated_tracks}
        return updated_tracks
    
    def calculate_iou(self, bbox1: List[int], bbox2: List[int]) -> float:
        """Calculate Intersection over Union (IoU) between two bounding boxes"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # Calculate union
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def process_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, List[Dict]]:
        """Process a single frame with hybrid tracking and classification"""
        # Step 1: ONNX detection for tracking
        onnx_detections = self.detect_with_onnx(frame)
        
        # Step 2: Update tracks and run YOLO classification
        tracks = self.update_tracks(onnx_detections, frame)
        
        # Step 3: Draw results on frame
        annotated_frame = self.draw_results(frame.copy(), tracks)
        
        return annotated_frame, tracks
    
    def draw_results(self, frame: np.ndarray, tracks: List[Dict]) -> np.ndarray:
        """Draw tracking and classification results on frame"""
        for track in tracks:
            bbox = track['bbox']
            track_id = track['track_id']
            confidence = track['confidence']
            
            x1, y1, x2, y2 = [int(coord) for coord in bbox]
            
            # Draw bounding box (green for tracked objects)
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Prepare label with tracking and classification info
            label_parts = [f"Track {track_id}"]
            
            if 'yolo_classification' in track:
                yolo_class = track['yolo_classification']
                if yolo_class['confidence'] > 0.3:
                    label_parts.append(f"{yolo_class['class_name']}: {yolo_class['confidence']:.2f}")
                else:
                    label_parts.append("Unknown")
            
            label = " | ".join(label_parts)
            
            # Draw label background
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            
            # Draw label text
            cv2.putText(frame, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        
        return frame


class HybridQuadrantProcessor:
    """Process four camera feeds with hybrid tracking and classification"""
    
    def __init__(self, onnx_model_path: str, yolo_model_path: str):
        self.onnx_model_path = onnx_model_path
        self.yolo_model_path = yolo_model_path
        self.output_size = (1920, 1080)
        self.quadrant_size = (960, 540)
        
    def process_videos(self, video_paths: Dict[int, str], output_path: str, 
                      max_frames: int = None) -> bool:
        """Process four camera videos with hybrid tracking"""
        try:
            logger.info("Starting hybrid tracking and classification pipeline...")
            
            # Initialize trackers for each camera
            trackers = {}
            for cam_id in range(4):
                trackers[cam_id] = HybridTracker(self.onnx_model_path, self.yolo_model_path)
            
            # Open video captures
            captures = {}
            for cam_id, video_path in video_paths.items():
                cap = cv2.VideoCapture(video_path)
                if not cap.isOpened():
                    logger.error(f"Failed to open video: {video_path}")
                    return False
                captures[cam_id] = cap
            
            # Get video properties
            fps = captures[0].get(cv2.CAP_PROP_FPS)
            total_frames = int(captures[0].get(cv2.CAP_PROP_FRAME_COUNT))
            
            if max_frames:
                total_frames = min(total_frames, max_frames)
            
            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, self.output_size)
            
            # Process frames
            frame_count = 0
            start_time = time.time()
            total_detections = 0
            
            logger.info(f"Processing {total_frames} frames at {fps} FPS...")
            
            while frame_count < total_frames:
                frames = {}
                all_tracks = {}
                
                # Read frames from all cameras
                all_frames_read = True
                for cam_id, cap in captures.items():
                    ret, frame = cap.read()
                    if not ret:
                        all_frames_read = False
                        break
                    frames[cam_id] = frame
                
                if not all_frames_read:
                    break
                
                # Process frames with hybrid tracking
                processed_frames = {}
                for cam_id, frame in frames.items():
                    annotated_frame, tracks = trackers[cam_id].process_frame(frame)
                    processed_frames[cam_id] = annotated_frame
                    all_tracks[cam_id] = tracks
                    total_detections += len(tracks)
                
                # Create quadrant layout
                output_frame = self.create_quadrant_layout(
                    processed_frames, all_tracks, frame_count, total_frames
                )
                
                # Write frame
                out.write(output_frame)
                frame_count += 1
                
                # Progress logging
                if frame_count % 30 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"Progress: {progress:.1f}% ({frame_count}/{total_frames})")
            
            # Cleanup
            for cap in captures.values():
                cap.release()
            out.release()
            
            # Final statistics
            end_time = time.time()
            total_time = end_time - start_time
            avg_fps = frame_count / total_time if total_time > 0 else 0
            
            logger.info(f"Hybrid pipeline completed!")
            logger.info(f"- Processed frames: {frame_count}")
            logger.info(f"- Total tracks: {total_detections}")
            logger.info(f"- Average FPS: {avg_fps:.2f}")
            logger.info(f"- Total time: {total_time:.2f} seconds")
            logger.info(f"- Output video: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error in hybrid pipeline: {e}")
            return False
    
    def create_quadrant_layout(self, frames: Dict[int, np.ndarray], 
                              tracks: Dict[int, List[Dict]], 
                              current_frame: int, total_frames: int) -> np.ndarray:
        """Create 2x2 quadrant layout with tracking results"""
        try:
            # Create output canvas
            output_frame = np.zeros((self.output_size[1], self.output_size[0], 3), dtype=np.uint8)
            
            # Define quadrant positions
            positions = {
                0: (0, 0),  # Top-left
                1: (self.quadrant_size[0], 0),  # Top-right
                2: (0, self.quadrant_size[1]),  # Bottom-left
                3: (self.quadrant_size[0], self.quadrant_size[1])  # Bottom-right
            }
            
            total_tracks = 0
            
            for cam_id in range(4):
                if cam_id not in frames:
                    continue
                
                # Resize frame to quadrant size
                frame = frames[cam_id]
                if frame is not None and frame.size > 0:
                    resized_frame = cv2.resize(frame, self.quadrant_size)
                else:
                    resized_frame = np.zeros((self.quadrant_size[1], self.quadrant_size[0], 3), dtype=np.uint8)
                
                # Place frame in quadrant
                x, y = positions[cam_id]
                output_frame[y:y+self.quadrant_size[1], x:x+self.quadrant_size[0]] = resized_frame
                
                # Add camera label
                cv2.putText(output_frame, f"Camera {cam_id}", 
                           (x + 10, y + 30), cv2.FONT_HERSHEY_SIMPLEX, 
                           1, (255, 255, 255), 2)
                
                # Add track count for this camera
                if cam_id in tracks:
                    track_count = len(tracks[cam_id])
                    total_tracks += track_count
                    cv2.putText(output_frame, f"Tracks: {track_count}", 
                               (x + 10, y + 60), cv2.FONT_HERSHEY_SIMPLEX, 
                               0.7, (0, 255, 0), 2)
            
            # Add global performance metrics
            progress = (current_frame / total_frames) * 100 if total_frames > 0 else 0
            cv2.putText(output_frame, f"Progress: {progress:.1f}% ({current_frame}/{total_frames})", 
                       (10, output_frame.shape[0] - 60), cv2.FONT_HERSHEY_SIMPLEX, 
                       1, (255, 255, 255), 2)
            
            cv2.putText(output_frame, f"Total Active Tracks: {total_tracks}", 
                       (10, output_frame.shape[0] - 30), cv2.FONT_HERSHEY_SIMPLEX, 
                       1, (255, 255, 255), 2)
            
            # Add timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cv2.putText(output_frame, timestamp, 
                       (output_frame.shape[1] - 300, output_frame.shape[0] - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            return output_frame
            
        except Exception as e:
            logger.error(f"Error creating quadrant layout: {e}")
            return np.zeros((self.output_size[1], self.output_size[0], 3), dtype=np.uint8)


def main():
    """Main function for hybrid tracking and classification pipeline"""
    # Configuration
    onnx_model_path = "yolow-l_product_and_hand_detector.onnx"
    yolo_model_path = "trained_models/amino_energy_grape_segmentation/weights/best.pt"
    
    # Video paths
    video_paths = {}
    quadcam_dir = Path("QuadCamTestVideos/AminoEnergyGrape")
    
    for cam_id in range(4):
        video_path = quadcam_dir / f"cam{cam_id}.mp4"
        if video_path.exists():
            video_paths[cam_id] = str(video_path)
        else:
            logger.error(f"Video not found: {video_path}")
            return False
    
    # Output configuration
    output_path = "hybrid_tracking_classification_results.mp4"
    max_frames = 300  # Process first 300 frames for testing
    
    # Initialize and run pipeline
    processor = HybridQuadrantProcessor(onnx_model_path, yolo_model_path)
    
    success = processor.process_videos(video_paths, output_path, max_frames)
    
    if success:
        logger.info("✅ Hybrid pipeline completed successfully!")
        print(f"\n🎯 SUCCESS: Hybrid tracking and classification results saved as '{output_path}'")
        print("\n📊 Pipeline Features:")
        print("   • ONNX model for stable object tracking")
        print("   • YOLO model for accurate product classification")
        print("   • Multi-camera quadrant display")
        print("   • Real-time performance metrics")
        return True
    else:
        logger.error("❌ Hybrid pipeline failed!")
        print("\n❌ FAILED: Hybrid pipeline execution failed. Check logs for details.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
