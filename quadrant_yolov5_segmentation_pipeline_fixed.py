#!/usr/bin/env python3
"""
Fixed Comprehensive Quadrant-Based YOLOv5 Segmentation Pipeline for Amino Energy Grape Detection

This script creates a complete pipeline that:
1. Converts LabelMe annotations to YOLO segmentation format
2. Trains YOLOv5 segmentation model on Amino Energy Grape dataset
3. Processes four camera feeds simultaneously
4. Displays results in a 2x2 grid layout with detection overlays
5. Generates amino_energy_quadcam_results_fixed.mp4 output

Fixed issues:
- Improved YOLOv5 results parsing
- Better error handling for detection results
- Enhanced overlay rendering
"""

import sys
import os
import time
import json
import cv2
import numpy as np
import torch
import yaml
from pathlib import Path
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
import logging
import shutil
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor
import argparse

# Add Hand_Test to path for YOLOv5 segmentation
HAND_TEST_DIR = Path("Hand_Test")
sys.path.insert(0, str(HAND_TEST_DIR))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('quadrant_pipeline_fixed.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FixedMultiCameraProcessor:
    """Fixed processor for multiple camera feeds with improved YOLOv5 handling"""
    
    def __init__(self, model_path: Path, camera_paths: Dict[int, Path]):
        self.model_path = Path(model_path)
        self.camera_paths = camera_paths
        self.model = None
        self.load_model()
        
    def load_model(self) -> bool:
        """Load trained YOLOv5 segmentation model"""
        try:
            # Load model using torch.hub
            self.model = torch.hub.load(
                str(HAND_TEST_DIR), 
                'custom', 
                path=str(self.model_path), 
                source='local'
            )
            self.model.conf = 0.25  # confidence threshold
            self.model.iou = 0.45   # NMS IoU threshold
            
            logger.info(f"Model loaded successfully from {self.model_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False
    
    def process_frame(self, frame: np.ndarray, cam_id: int) -> Dict:
        """Process a single frame with YOLOv5 segmentation - FIXED VERSION"""
        try:
            if self.model is None:
                return {"detections": [], "masks": [], "frame": frame}
            
            # Run inference
            results = self.model(frame)
            
            # Extract results - FIXED PARSING
            detections = []
            masks = []
            
            # Get the first result (batch size 1)
            result = results.xyxy[0] if hasattr(results, 'xyxy') else None
            
            if result is not None and len(result) > 0:
                # Convert to numpy if it's a tensor
                if hasattr(result, 'cpu'):
                    result = result.cpu().numpy()
                
                for detection in result:
                    # detection format: [x1, y1, x2, y2, confidence, class]
                    if len(detection) >= 6:
                        x1, y1, x2, y2, conf, cls = detection[:6]
                        detections.append({
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],
                            'confidence': float(conf),
                            'class': 'Amino_Energy_Grape',
                            'class_id': int(cls)
                        })
            
            # Get segmentation masks if available
            if hasattr(results, 'masks') and results.masks is not None:
                try:
                    masks_data = results.masks.data
                    if hasattr(masks_data, 'cpu'):
                        masks = masks_data.cpu().numpy()
                    else:
                        masks = masks_data
                except Exception as e:
                    logger.debug(f"Error extracting masks: {e}")
                    masks = []
            
            # Create annotated frame
            annotated_frame = self.draw_detections(frame.copy(), detections, masks)
            
            return {
                "detections": detections,
                "masks": masks,
                "frame": annotated_frame,
                "raw_results": results
            }
            
        except Exception as e:
            logger.error(f"Error processing frame for camera {cam_id}: {e}")
            return {"detections": [], "masks": [], "frame": frame}
    
    def draw_detections(self, frame: np.ndarray, detections: List[Dict], masks: List) -> np.ndarray:
        """Draw detection results on frame"""
        try:
            # Draw bounding boxes
            for detection in detections:
                bbox = detection['bbox']
                confidence = detection['confidence']
                class_name = detection['class']
                
                x1, y1, x2, y2 = [int(coord) for coord in bbox]
                
                # Draw bounding box
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # Draw confidence score
                label = f"{class_name}: {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                             (x1 + label_size[0], y1), (0, 255, 0), -1)
                cv2.putText(frame, label, (x1, y1 - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            
            # Draw segmentation masks
            if len(masks) > 0:
                for i, mask in enumerate(masks):
                    if len(mask.shape) == 2:  # 2D mask
                        # Resize mask to frame size
                        mask_resized = cv2.resize(mask.astype(np.uint8), 
                                                (frame.shape[1], frame.shape[0]))
                        
                        # Create colored overlay
                        colored_mask = np.zeros_like(frame)
                        colored_mask[mask_resized > 0.5] = [0, 255, 255]  # Yellow mask
                        
                        # Blend with frame
                        frame = cv2.addWeighted(frame, 0.7, colored_mask, 0.3, 0)
            
            return frame
            
        except Exception as e:
            logger.error(f"Error drawing detections: {e}")
            return frame

class FixedQuadrantDisplayManager:
    """Fixed manager for 2x2 quadrant display with improved overlays"""
    
    def __init__(self, output_size: Tuple[int, int] = (1920, 1080)):
        self.output_size = output_size
        self.quadrant_size = (output_size[0] // 2, output_size[1] // 2)
        
    def create_quadrant_layout(self, frames: Dict[int, np.ndarray], 
                             detection_results: Dict[int, Dict],
                             performance_metrics: Dict) -> np.ndarray:
        """Create 2x2 quadrant layout with overlays - FIXED VERSION"""
        try:
            # Create output canvas
            output_frame = np.zeros((self.output_size[1], self.output_size[0], 3), dtype=np.uint8)
            
            # Define quadrant positions
            positions = {
                0: (0, 0),  # Top-left
                1: (self.quadrant_size[0], 0),  # Top-right
                2: (0, self.quadrant_size[1]),  # Bottom-left
                3: (self.quadrant_size[0], self.quadrant_size[1])  # Bottom-right
            }
            
            for cam_id in range(4):
                if cam_id not in frames:
                    continue
                
                # Resize frame to quadrant size
                frame = frames[cam_id]
                if frame is not None and frame.size > 0:
                    resized_frame = cv2.resize(frame, self.quadrant_size)
                else:
                    # Create black frame if no frame available
                    resized_frame = np.zeros((self.quadrant_size[1], self.quadrant_size[0], 3), dtype=np.uint8)
                
                # Place frame in quadrant
                x, y = positions[cam_id]
                output_frame[y:y+self.quadrant_size[1], x:x+self.quadrant_size[0]] = resized_frame
                
                # Add camera label
                cv2.putText(output_frame, f"Camera {cam_id}", 
                           (x + 10, y + 30), cv2.FONT_HERSHEY_SIMPLEX, 
                           1, (255, 255, 255), 2)
                
                # Add detection count for this camera
                if cam_id in detection_results:
                    det_count = len(detection_results[cam_id].get("detections", []))
                    cv2.putText(output_frame, f"Detections: {det_count}", 
                               (x + 10, y + 60), cv2.FONT_HERSHEY_SIMPLEX, 
                               0.7, (0, 255, 0), 2)
            
            # Add performance metrics overlay
            output_frame = self.add_performance_overlay(output_frame, performance_metrics)
            
            return output_frame
            
        except Exception as e:
            logger.error(f"Error creating quadrant layout: {e}")
            return np.zeros((self.output_size[1], self.output_size[0], 3), dtype=np.uint8)
    
    def add_performance_overlay(self, frame: np.ndarray, metrics: Dict) -> np.ndarray:
        """Add performance metrics overlay - ENHANCED VERSION"""
        try:
            # Add FPS counter
            fps = metrics.get('fps', 0)
            cv2.putText(frame, f"FPS: {fps:.1f}", (10, frame.shape[0] - 90), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # Add detection count
            total_detections = metrics.get('total_detections', 0)
            cv2.putText(frame, f"Total Detections: {total_detections}", 
                       (10, frame.shape[0] - 60), cv2.FONT_HERSHEY_SIMPLEX, 
                       1, (255, 255, 255), 2)
            
            # Add frame progress
            current_frame = metrics.get('frame', 0)
            total_frames = metrics.get('total_frames', 0)
            if total_frames > 0:
                progress = (current_frame / total_frames) * 100
                cv2.putText(frame, f"Progress: {progress:.1f}% ({current_frame}/{total_frames})", 
                           (10, frame.shape[0] - 30), cv2.FONT_HERSHEY_SIMPLEX, 
                           1, (255, 255, 255), 2)
            
            # Add timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cv2.putText(frame, timestamp, (frame.shape[1] - 300, frame.shape[0] - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            return frame
            
        except Exception as e:
            logger.error(f"Error adding performance overlay: {e}")
            return frame

class FixedQuadrantVideoProcessor:
    """Fixed main pipeline class for quadrant-based video processing"""
    
    def __init__(self):
        self.labelme_dir = Path("Images/Amino_Energy_Grape-segmentation-labelme-extracted")
        self.quadcam_dir = Path("QuadCamTestVideos/AminoEnergyGrape")
        self.output_dir = Path("yolo_dataset")
        self.models_dir = Path("trained_models")
        self.output_video = "amino_energy_quadcam_results_fixed.mp4"
        
    def process_videos_fixed(self, model_path: Path, max_frames: int = None) -> bool:
        """Process four camera videos with fixed detection handling"""
        try:
            logger.info("Starting fixed video processing...")
            
            # Check if all camera videos exist
            camera_paths = {}
            for cam_id in range(4):
                video_path = self.quadcam_dir / f"cam{cam_id}.mp4"
                if not video_path.exists():
                    logger.error(f"Video not found: {video_path}")
                    return False
                camera_paths[cam_id] = video_path
            
            # Initialize fixed processor and display manager
            processor = FixedMultiCameraProcessor(model_path, camera_paths)
            display_manager = FixedQuadrantDisplayManager()
            
            # Open video captures
            captures = {}
            for cam_id, video_path in camera_paths.items():
                cap = cv2.VideoCapture(str(video_path))
                if not cap.isOpened():
                    logger.error(f"Failed to open video: {video_path}")
                    return False
                captures[cam_id] = cap
            
            # Get video properties
            fps = captures[0].get(cv2.CAP_PROP_FPS)
            total_frames = int(captures[0].get(cv2.CAP_PROP_FRAME_COUNT))
            
            if max_frames:
                total_frames = min(total_frames, max_frames)
            
            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(
                self.output_video, fourcc, fps, 
                display_manager.output_size
            )
            
            # Process frames
            frame_count = 0
            start_time = time.time()
            total_detections = 0
            
            logger.info(f"Processing {total_frames} frames at {fps} FPS...")
            
            while frame_count < total_frames:
                frames = {}
                detection_results = {}
                
                # Read frames from all cameras
                all_frames_read = True
                for cam_id, cap in captures.items():
                    ret, frame = cap.read()
                    if not ret:
                        all_frames_read = False
                        break
                    frames[cam_id] = frame
                
                if not all_frames_read:
                    break
                
                # Process frames with detection
                for cam_id, frame in frames.items():
                    result = processor.process_frame(frame, cam_id)
                    detection_results[cam_id] = result
                    frames[cam_id] = result['frame']  # Use annotated frame
                    total_detections += len(result['detections'])
                
                # Calculate performance metrics
                current_time = time.time()
                elapsed_time = current_time - start_time
                current_fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                
                performance_metrics = {
                    'fps': current_fps,
                    'total_detections': total_detections,
                    'frame': frame_count,
                    'total_frames': total_frames
                }
                
                # Create quadrant layout
                output_frame = display_manager.create_quadrant_layout(
                    frames, detection_results, performance_metrics
                )
                
                # Write frame
                out.write(output_frame)
                
                frame_count += 1
                
                # Progress logging
                if frame_count % 30 == 0:  # Log every 30 frames
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"Progress: {progress:.1f}% ({frame_count}/{total_frames})")
            
            # Cleanup
            for cap in captures.values():
                cap.release()
            out.release()
            
            # Final statistics
            end_time = time.time()
            total_time = end_time - start_time
            avg_fps = frame_count / total_time if total_time > 0 else 0
            
            logger.info(f"Fixed video processing completed!")
            logger.info(f"- Processed frames: {frame_count}")
            logger.info(f"- Total detections: {total_detections}")
            logger.info(f"- Average FPS: {avg_fps:.2f}")
            logger.info(f"- Total time: {total_time:.2f} seconds")
            logger.info(f"- Output video: {self.output_video}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error in fixed video processing: {e}")
            return False

def main():
    """Main function for fixed pipeline"""
    parser = argparse.ArgumentParser(
        description="Fixed Quadrant-based YOLOv5 Segmentation Pipeline"
    )
    parser.add_argument("--model-path", type=str, required=True,
                       help="Path to trained model")
    parser.add_argument("--max-frames", type=int, default=None, 
                       help="Maximum frames to process (default: all)")
    
    args = parser.parse_args()
    
    # Initialize fixed pipeline
    pipeline = FixedQuadrantVideoProcessor()
    
    try:
        model_path = Path(args.model_path)
        if not model_path.exists():
            logger.error(f"Model not found: {model_path}")
            return False
        
        success = pipeline.process_videos_fixed(model_path, args.max_frames)
        
        if success:
            logger.info("Fixed pipeline completed successfully!")
            print(f"\n✅ SUCCESS: Fixed output video saved as '{pipeline.output_video}'")
            return True
        else:
            logger.error("Fixed pipeline failed!")
            print("\n❌ FAILED: Fixed pipeline execution failed. Check logs for details.")
            return False
            
    except KeyboardInterrupt:
        logger.info("Fixed pipeline interrupted by user")
        print("\n⚠️  INTERRUPTED: Fixed pipeline stopped by user")
        return False
    except Exception as e:
        logger.error(f"Unexpected error in fixed pipeline: {e}")
        print(f"\n❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
