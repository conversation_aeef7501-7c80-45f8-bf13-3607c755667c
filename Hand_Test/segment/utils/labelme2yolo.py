#!/usr/bin/env python3
"""
Utility to convert labelme annotations to YOLOv5 segmentation format
"""

import os
import json
import shutil
import random
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import numpy as np
import cv2


def convert_labelme_to_yolo(
    labelme_dir: str,
    output_dir: str,
    train_val_split: float = 0.8
) -> bool:
    """
    Convert labelme annotations to YOLOv5 segmentation format.
    
    Args:
        labelme_dir: Directory containing labelme annotations and images
        output_dir: Output directory for YOLOv5 dataset
        train_val_split: Ratio of training to validation data
        
    Returns:
        bool: True if conversion was successful
    """
    labelme_dir = Path(labelme_dir)
    output_dir = Path(output_dir)
    
    # Create output directories
    images_train_dir = output_dir / "images" / "train"
    images_val_dir = output_dir / "images" / "val"
    labels_train_dir = output_dir / "labels" / "train"
    labels_val_dir = output_dir / "labels" / "val"
    
    for dir_path in [images_train_dir, images_val_dir, labels_train_dir, labels_val_dir]:
        dir_path.mkdir(exist_ok=True, parents=True)
    
    # Get all JSON files
    json_files = list(labelme_dir.glob("*.json"))
    random.shuffle(json_files)
    
    # Split into train and val
    split_idx = int(len(json_files) * train_val_split)
    train_files = json_files[:split_idx]
    val_files = json_files[split_idx:]
    
    print(f"Converting {len(train_files)} training and {len(val_files)} validation files")
    
    # Process training files
    process_files(train_files, labelme_dir, images_train_dir, labels_train_dir)
    
    # Process validation files
    process_files(val_files, labelme_dir, images_val_dir, labels_val_dir)
    
    print(f"Conversion complete. Dataset saved to {output_dir}")
    return True


def process_files(
    json_files: List[Path],
    labelme_dir: Path,
    images_output_dir: Path,
    labels_output_dir: Path
) -> None:
    """
    Process a list of JSON files and convert them to YOLOv5 format.
    
    Args:
        json_files: List of JSON file paths
        labelme_dir: Directory containing labelme annotations and images
        images_output_dir: Output directory for images
        labels_output_dir: Output directory for labels
    """
    for json_file in json_files:
        # Get corresponding image file
        img_file = None
        for ext in [".jpg", ".jpeg", ".png"]:
            potential_img = json_file.with_suffix(ext)
            if potential_img.exists():
                img_file = potential_img
                break
        
        if img_file is None:
            print(f"Warning: No image found for {json_file}")
            continue
        
        # Load JSON data
        with open(json_file, "r") as f:
            data = json.load(f)
        
        # Get image dimensions
        img = cv2.imread(str(img_file))
        if img is None:
            print(f"Warning: Could not read image {img_file}")
            continue
        
        img_height, img_width = img.shape[:2]
        
        # Create YOLO label file
        label_file = labels_output_dir / (img_file.stem + ".txt")
        
        with open(label_file, "w") as f:
            for shape in data["shapes"]:
                if shape["shape_type"] != "polygon":
                    continue
                
                # Get class ID (assuming "Amino_Energy_Grape" is class 0)
                class_id = 0
                
                # Get polygon points
                points = shape["points"]
                
                # Normalize coordinates
                normalized_points = []
                for x, y in points:
                    normalized_points.append(x / img_width)
                    normalized_points.append(y / img_height)
                
                # Write to label file
                f.write(f"{class_id} " + " ".join([f"{p:.6f}" for p in normalized_points]) + "\n")
        
        # Copy image to output directory
        shutil.copy(img_file, images_output_dir / img_file.name)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Convert labelme annotations to YOLOv5 segmentation format")
    parser.add_argument("--labelme-dir", required=True, help="Directory containing labelme annotations and images")
    parser.add_argument("--output-dir", required=True, help="Output directory for YOLOv5 dataset")
    parser.add_argument("--train-val-split", type=float, default=0.8, help="Ratio of training to validation data")
    
    args = parser.parse_args()
    
    convert_labelme_to_yolo(
        labelme_dir=args.labelme_dir,
        output_dir=args.output_dir,
        train_val_split=args.train_val_split
    )