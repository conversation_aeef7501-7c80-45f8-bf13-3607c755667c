#!/usr/bin/env python3
"""
Test ONNX Model Detection
========================

This script tests the ONNX model to verify it's working correctly
and can detect objects in the video frames.
"""

import cv2
import numpy as np
import onnxruntime as ort
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_onnx_model():
    """Test ONNX model with a sample frame"""
    
    # Load ONNX model
    onnx_model_path = "yolow-l_product_and_hand_detector.onnx"
    
    if not Path(onnx_model_path).exists():
        logger.error(f"ONNX model not found: {onnx_model_path}")
        return False
    
    try:
        session = ort.InferenceSession(onnx_model_path)
        input_name = session.get_inputs()[0].name
        output_names = [output.name for output in session.get_outputs()]
        
        logger.info(f"✅ ONNX model loaded successfully")
        logger.info(f"Input name: {input_name}")
        logger.info(f"Output names: {output_names}")
        
        # Get input shape
        input_shape = session.get_inputs()[0].shape
        logger.info(f"Expected input shape: {input_shape}")
        
    except Exception as e:
        logger.error(f"Error loading ONNX model: {e}")
        return False
    
    # Load a test frame
    video_path = "QuadCamTestVideos/AminoEnergyGrape/cam0.mp4"
    
    if not Path(video_path).exists():
        logger.error(f"Test video not found: {video_path}")
        return False
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logger.error(f"Failed to open video: {video_path}")
        return False
    
    # Read first frame
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        logger.error("Failed to read frame from video")
        return False
    
    logger.info(f"✅ Test frame loaded: {frame.shape}")
    
    # Preprocess frame
    input_size = 640
    resized = cv2.resize(frame, (input_size, input_size))
    rgb_frame = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
    normalized = rgb_frame.astype(np.float32) / 255.0
    input_data = np.transpose(normalized, (2, 0, 1))
    input_data = np.expand_dims(input_data, axis=0)
    
    logger.info(f"✅ Frame preprocessed: {input_data.shape}")
    
    # Run inference
    try:
        outputs = session.run(output_names, {input_name: input_data})
        logger.info(f"✅ ONNX inference successful")
        logger.info(f"Output shapes: {[out.shape for out in outputs]}")
        
        # Analyze outputs
        for i, output in enumerate(outputs):
            logger.info(f"Output {i}: shape={output.shape}, dtype={output.dtype}")
            logger.info(f"Output {i}: min={output.min():.4f}, max={output.max():.4f}, mean={output.mean():.4f}")
            
            # Look for detections
            if len(output.shape) >= 2:
                # Check if this looks like detection output
                if output.shape[-1] >= 6:  # At least x,y,w,h,conf,class
                    logger.info(f"Output {i} looks like detection format")
                    
                    # Count potential detections
                    if len(output.shape) == 3:
                        detections = output[0]  # Remove batch dimension
                    else:
                        detections = output
                    
                    # Count detections above threshold
                    if detections.shape[-1] >= 5:
                        confidences = detections[:, 4]
                        high_conf = np.sum(confidences > 0.1)
                        very_high_conf = np.sum(confidences > 0.5)
                        
                        logger.info(f"Potential detections with conf > 0.1: {high_conf}")
                        logger.info(f"Potential detections with conf > 0.5: {very_high_conf}")
                        
                        if high_conf > 0:
                            # Show top detections
                            top_indices = np.argsort(confidences)[-5:][::-1]
                            logger.info("Top 5 detections:")
                            for idx in top_indices:
                                if confidences[idx] > 0.01:
                                    det = detections[idx]
                                    logger.info(f"  Detection {idx}: conf={confidences[idx]:.4f}, "
                                              f"bbox=[{det[0]:.1f}, {det[1]:.1f}, {det[2]:.1f}, {det[3]:.1f}]")
        
        return True
        
    except Exception as e:
        logger.error(f"Error running ONNX inference: {e}")
        return False

if __name__ == "__main__":
    success = test_onnx_model()
    if success:
        print("\n✅ ONNX model test completed successfully!")
    else:
        print("\n❌ ONNX model test failed!")
