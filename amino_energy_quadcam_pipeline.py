#!/usr/bin/env python3
"""
Amino Energy Grape Quadrant-Based Video Processing Pipeline

This script creates a comprehensive pipeline that:
1. Uses YOLOv5 segmentation for object detection
2. Processes four camera feeds simultaneously
3. Displays results in a 2x2 grid layout
4. Overlays detection results and performance metrics
"""

import sys
import os
import time
import json
import cv2
import numpy as np
import torch
from pathlib import Path
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
import logging

# Add Hand_Test to path for YOLOv5 segmentation
HAND_TEST_DIR = Path("Hand_Test")
sys.path.insert(0, str(HAND_TEST_DIR))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("AminoEnergyQuadcamPipeline")

class AminoEnergyQuadcamPipeline:
    """
    Comprehensive pipeline for processing Amino Energy Grape videos
    using YOLOv5 segmentation and quadrant-based visualization.
    """
    
    def __init__(self):
        """Initialize the pipeline with default settings."""
        self.project_dir = Path.cwd()
        
        # Data directories
        self.training_data_dir = self.project_dir / "Images" / "Amino_Energy_Grape-segmentation-labelme-extracted"
        self.quadcam_dir = self.project_dir / "QuadCamTestVideos" / "AminoEnergyGrape"
        
        # Output directories
        self.results_dir = self.project_dir / "amino_energy_quadcam_results"
        self.models_dir = self.results_dir / "trained_models"
        
        # Create output directories
        self.results_dir.mkdir(exist_ok=True, parents=True)
        self.models_dir.mkdir(exist_ok=True, parents=True)
        
        # Model paths
        self.dataset_yaml_path = self.results_dir / "dataset.yaml"
        self.trained_model_path = self.models_dir / "amino_energy_grape_segmentation.pt"
        
        # Output video path
        self.output_video_path = self.results_dir / "amino_energy_quadcam_results.mp4"
        
        # Performance tracking
        self.metrics = {
            "training_time": 0,
            "inference_fps": 0,
            "total_detections": 0,
            "detection_counts": {f"cam{i}": 0 for i in range(4)},
            "avg_confidence": {f"cam{i}": 0 for i in range(4)}
        }
        
        logger.info("Initialized Amino Energy Quadcam Pipeline")
    
    def prepare_dataset(self) -> bool:
        """
        Prepare the YOLOv5 segmentation dataset from labelme annotations.
        """
        logger.info("Preparing YOLOv5 segmentation dataset")
        
        try:
            # Check if training data exists
            if not self.training_data_dir.exists():
                logger.error(f"Training data directory not found: {self.training_data_dir}")
                return False
            
            # Count images and annotations
            image_files = list(self.training_data_dir.glob("*.jpg")) + list(self.training_data_dir.glob("*.png"))
            json_files = list(self.training_data_dir.glob("*.json"))
            
            logger.info(f"Found {len(image_files)} images and {len(json_files)} annotations")
            
            if len(image_files) == 0 or len(json_files) == 0:
                logger.error("No images or annotations found")
                return False
            
            # Create dataset directory structure
            dataset_dir = self.results_dir / "yolov5_dataset"
            images_dir = dataset_dir / "images"
            labels_dir = dataset_dir / "labels"
            
            for split in ["train", "val"]:
                (images_dir / split).mkdir(exist_ok=True, parents=True)
                (labels_dir / split).mkdir(exist_ok=True, parents=True)
            
            # Convert labelme annotations to YOLOv5 format
            from segment.utils.labelme2yolo import convert_labelme_to_yolo
            
            logger.info("Converting labelme annotations to YOLOv5 format")
            convert_labelme_to_yolo(
                labelme_dir=str(self.training_data_dir),
                output_dir=str(dataset_dir),
                train_val_split=0.8
            )
            
            # Create dataset.yaml
            dataset_yaml = {
                "path": str(dataset_dir),
                "train": "images/train",
                "val": "images/val",
                "names": {
                    0: "Amino_Energy_Grape"
                }
            }
            
            with open(self.dataset_yaml_path, "w") as f:
                yaml.dump(dataset_yaml, f, sort_keys=False)
            
            logger.info(f"Dataset prepared successfully: {self.dataset_yaml_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error preparing dataset: {str(e)}")
            return False
    
    def train_model(self) -> bool:
        """
        Train YOLOv5 segmentation model on Amino Energy Grape dataset.
        """
        logger.info("Training YOLOv5 segmentation model")
        
        try:
            # Import YOLOv5 segment module
            sys.path.insert(0, str(HAND_TEST_DIR / "segment"))
            from segment import train
            
            # Set training arguments
            train_args = {
                "data": str(self.dataset_yaml_path),
                "weights": "yolov5s-seg.pt",  # Start with pretrained weights
                "epochs": 50,
                "batch_size": 16,
                "imgsz": 640,
                "project": str(self.models_dir),
                "name": "amino_energy_grape_segmentation",
                "exist_ok": True,
                "patience": 10,  # Early stopping patience
            }
            
            # Record start time
            start_time = time.time()
            
            # Train the model
            results = train.run(**train_args)
            
            # Record training time
            self.metrics["training_time"] = time.time() - start_time
            
            # Get best model path
            self.trained_model_path = Path(results.best)
            
            logger.info(f"Model trained successfully: {self.trained_model_path}")
            logger.info(f"Training time: {self.metrics['training_time']:.2f} seconds")
            
            return True
            
        except Exception as e:
            logger.error(f"Error training model: {str(e)}")
            return False
    
    def process_quadcam_videos(self) -> bool:
        """
        Process all four QuadCam videos and create a composite output video.
        """
        logger.info("Processing QuadCam videos")
        
        try:
            # Check if QuadCam videos exist
            video_paths = {}
            for cam_id in range(4):
                video_path = self.quadcam_dir / f"cam{cam_id}.mp4"
                if not video_path.exists():
                    logger.error(f"Video not found: {video_path}")
                    return False
                video_paths[cam_id] = video_path
            
            logger.info(f"Found all 4 camera videos")
            
            # Load YOLOv5 segmentation model
            sys.path.insert(0, str(HAND_TEST_DIR / "segment"))
            from segment import predict
            
            # Load model
            model = torch.hub.load(
                str(HAND_TEST_DIR), 
                'custom', 
                path=str(self.trained_model_path), 
                source='local'
            )
            model.conf = 0.25  # Confidence threshold
            model.iou = 0.45   # IoU threshold
            
            # Open all video captures
            caps = {}
            for cam_id, video_path in video_paths.items():
                caps[cam_id] = cv2.VideoCapture(str(video_path))
                if not caps[cam_id].isOpened():
                    logger.error(f"Failed to open video: {video_path}")
                    return False
            
            # Get video properties
            width = int(caps[0].get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(caps[0].get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = caps[0].get(cv2.CAP_PROP_FPS)
            
            # Create output video writer
            output_width = width * 2
            output_height = height * 2
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(
                str(self.output_video_path),
                fourcc,
                fps,
                (output_width, output_height)
            )
            
            # Process frames
            frame_count = 0
            processing_times = []
            detection_counts = {cam_id: 0 for cam_id in range(4)}
            confidence_sums = {cam_id: 0 for cam_id in range(4)}
            
            logger.info(f"Starting video processing")
            
            while True:
                # Read frames from all cameras
                frames = {}
                all_read = True
                
                for cam_id, cap in caps.items():
                    ret, frame = cap.read()
                    if not ret:
                        all_read = False
                        break
                    frames[cam_id] = frame
                
                if not all_read:
                    break
                
                frame_count += 1
                start_time = time.time()
                
                # Process each frame with YOLOv5 segmentation
                processed_frames = {}
                
                for cam_id, frame in frames.items():
                    # Run inference
                    results = model(frame)
                    
                    # Get detections
                    detections = results.pandas().xyxy[0]
                    
                    # Count detections
                    num_detections = len(detections)
                    detection_counts[cam_id] += num_detections
                    
                    # Calculate average confidence
                    if num_detections > 0:
                        confidence_sums[cam_id] += detections['confidence'].sum()
                    
                    # Draw results on frame
                    processed_frame = results.render()[0]
                    
                    # Add camera label and metrics
                    cv2.putText(
                        processed_frame,
                        f"Cam {cam_id}",
                        (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        1,
                        (255, 255, 255),
                        2
                    )
                    
                    cv2.putText(
                        processed_frame,
                        f"Detections: {num_detections}",
                        (10, 70),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.7,
                        (255, 255, 255),
                        2
                    )
                    
                    processed_frames[cam_id] = processed_frame
                
                # Create composite frame (2x2 grid)
                composite_frame = np.zeros((output_height, output_width, 3), dtype=np.uint8)
                
                # Arrange frames in quadrants
                composite_frame[:height, :width] = processed_frames[0]  # Top-left
                composite_frame[:height, width:] = processed_frames[1]  # Top-right
                composite_frame[height:, :width] = processed_frames[2]  # Bottom-left
                composite_frame[height:, width:] = processed_frames[3]  # Bottom-right
                
                # Calculate processing time and FPS
                processing_time = time.time() - start_time
                processing_times.append(processing_time)
                current_fps = 1.0 / processing_time if processing_time > 0 else 0
                
                # Add global metrics to composite frame
                cv2.putText(
                    composite_frame,
                    f"Frame: {frame_count}",
                    (10, output_height - 70),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.7,
                    (255, 255, 255),
                    2
                )
                
                cv2.putText(
                    composite_frame,
                    f"FPS: {current_fps:.2f}",
                    (10, output_height - 30),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.7,
                    (255, 255, 255),
                    2
                )
                
                # Write frame to output video
                out.write(composite_frame)
                
                # Log progress
                if frame_count % 10 == 0:
                    logger.info(f"Processed {frame_count} frames, FPS: {current_fps:.2f}")
            
            # Calculate final metrics
            self.metrics["inference_fps"] = 1.0 / np.mean(processing_times) if processing_times else 0
            self.metrics["total_detections"] = sum(detection_counts.values())
            self.metrics["detection_counts"] = detection_counts
            
            for cam_id in range(4):
                if detection_counts[cam_id] > 0:
                    self.metrics["avg_confidence"][f"cam{cam_id}"] = confidence_sums[cam_id] / detection_counts[cam_id]
            
            # Clean up
            for cap in caps.values():
                cap.release()
            out.release()
            
            logger.info(f"Video processing complete: {self.output_video_path}")
            logger.info(f"Processed {frame_count} frames")
            logger.info(f"Average FPS: {self.metrics['inference_fps']:.2f}")
            logger.info(f"Total detections: {self.metrics['total_detections']}")
            
            # Save metrics
            metrics_path = self.results_dir / "metrics.json"
            with open(metrics_path, "w") as f:
                json.dump(self.metrics, f, indent=2)
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing videos: {str(e)}")
            return False
    
    def run_pipeline(self) -> bool:
        """
        Run the complete pipeline.
        """
        logger.info("="*50)
        logger.info("STARTING AMINO ENERGY QUADCAM PIPELINE")
        logger.info("="*50)
        
        # Step 1: Prepare dataset
        logger.info("\nSTEP 1: Preparing dataset")
        if not self.prepare_dataset():
            logger.error("Failed to prepare dataset")
            return False
        
        # Step 2: Train model
        logger.info("\nSTEP 2: Training model")
        if not self.train_model():
            logger.error("Failed to train model")
            return False
        
        # Step 3: Process videos
        logger.info("\nSTEP 3: Processing videos")
        if not self.process_quadcam_videos():
            logger.error("Failed to process videos")
            return False
        
        logger.info("\n" + "="*50)
        logger.info("PIPELINE COMPLETED SUCCESSFULLY")
        logger.info("="*50)
        logger.info(f"Output video: {self.output_video_path}")
        logger.info(f"Metrics: {self.results_dir / 'metrics.json'}")
        
        return True


def main():
    """Main function to run the pipeline."""
    try:
        # Import required modules
        import yaml
        from segment.utils.labelme2yolo import convert_labelme_to_yolo
    except ImportError as e:
        logger.error(f"Required module not found: {str(e)}")
        logger.error("Please install required dependencies: pip install -r requirements.txt")
        return False
    
    # Create and run pipeline
    pipeline = AminoEnergyQuadcamPipeline()
    success = pipeline.run_pipeline()
    
    return success


if __name__ == "__main__":
    main()